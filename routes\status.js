
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// routes/status.js
//
// Status routes
//=============================================================================================
/**
 * @module routes/data
 */

const { API_ROOT, mac } = require('../System.js')
const { ioServers }     = require('../Global.js')
const { objectExclude, stringToBoolean } = require('../Util.js')
const { AUTH_OPERATOR } = require('../AppLoginAuth.js')

const os = require('os');
const process = require('process');
const checkDiskSpace = require('check-disk-space').default;

async function getSystemInfo() {
	// const memoryUsage = process.memoryUsage();
	// const freeMemory = os.freemem();

	// Default to root directory (Unix) or C:\ (Windows)
	const diskPath = process.platform === 'win32' ? 'C:/' : '/';

	try {
		const diskSpace = await checkDiskSpace(diskPath);

		return {
			memory: {
				app: process.memoryUsage().rss,
				used: os.totalmem() - os.freemem(),
				free: os.freemem(),
				total: os.totalmem(),
			},
			disk: {
				used: diskSpace.size - diskSpace.free,
				free: diskSpace.free,
				total: diskSpace.size,
			}
			// nodeAppMemoryUsed: memoryUsage.rss,     // bytes
			// systemFreeMemory: freeMemory,           // bytes
			// diskFreeSpace: diskSpace.free,          // bytes
			// diskTotalSpace: diskSpace.size          // bytes
		};
	} catch (err) {
		console.error('Error getting disk space:', err);
		return null;
	}
}

// Example usage:
getSystemInfo().then(console.log);


//----------------------------------------------------------------------------------------------------
/**
 * Routes for the info page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function statusRoutes(fastify, options, done)
{
	// System info
	fastify.get(API_ROOT + '/status/system', async (request, reply) => {

		const systemInfo = await getSystemInfo();

		return reply.send(systemInfo)
	})

	// Register state route
	fastify.get(API_ROOT + '/status/servers', async (request, reply) => {

		const r = []
		// const verbose = request.query.verbose !== undefined && Number(request.query.verbose)

		Object.keys(ioServers).forEach(serverName => {
			if(ioServers[serverName].requestQueue.length > 0)
			{
				const ioServer = stringToBoolean(request.query.verbose) ? ioServers[serverName] : objectExclude(ioServers[serverName], 'requestQueue')
				try {
					ioServer.readyState = ioServer.modbusClient.client.readyState
				} catch(e) {}
				r.push(ioServer)
			}
		})

		return reply.send(r)
	})

	// fastify.get(API_ROOT + '/status/servers/reset', async (request, reply) => {
	// 	const serverNames = Object.keys(ioServers)
	// 	serverNames.forEach(serverName => {
	// 		const  ioServer = ioServers[serverName]
	// 		if(ioServer.requestQueue.length > 0)
	// 		{
	// 			ioServer.xferCount = 0
	// 			ioServer.error.count = 0
	// 			ioServer.error.timestamp = 0
	// 		}
	// 	})
	// 	return reply.send({message: 'Stats reset for ' + serverNames.length + ' servers'})
	// })

	fastify.delete(API_ROOT + '/status/servers', AUTH_OPERATOR, async (request, reply) => {
		const serverNames = Object.keys(ioServers)
		serverNames.forEach(serverName => {
			const  ioServer = ioServers[serverName]
			if(ioServer.requestQueue.length > 0)
			{
				ioServer.xferCount = 0
				ioServer.error.count = 0
				ioServer.error.timestamp = 0
			}
		})
		return reply.send({message: 'Stats reset for ' + serverNames.length + ' servers'})
	})


	done()
}


module.exports = statusRoutes
