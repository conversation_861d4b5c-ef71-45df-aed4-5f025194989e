//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//* AppLoginAuth.js
//*
//* The appliccation specific login authentication
//*
//******************************************************************************************
console.log('========== AppLoginAuth.js =========')

const { getUsersFile, secrets } = require('./System.js')
const { authBasic, jwtSign, jwtVerify, authJWT } = require('./HttpServerAuth.js')
const bcrypt = require('bcryptjs')
const { objectExclude } = require('./Util.js')

/**
 * Application Specific preHandler for the login route
 *
 * @param {object} req - The standard fastify request object
 * @param {object} reply - The standard fastify reply object
 * @param {object} done - The standard fastify done function
 * @returns {void}
 *
 */
function appAuthBasic(req, reply, done)
{
	authBasic(req, reply, done, appVerifyUser)
	// req.user should have the user object
}

/**
 * Application Specific function to verify the user
 *
 * @param {string} user - The username (email)
 * @param {string} pass - The password
 * @returns {object||boolean} - The user object if the user is valid, false otherwise
 *
 */
function appVerifyUser(user, pass)
{
	const users = getUsersFile()
	const myUser = users.find(u => u.user === user)
	if (myUser && bcrypt.compareSync(pass, myUser.pass))
	{
		return objectExclude(myUser, 'pass') // Return all user information except password
	}
	return false
}

function authJwtPart(req, reply)
{
	if(!req.headers.authorization)
	{
		reply.code(401).send({ message: 'No authorization header' })
		return
	}

	const a = req.headers.authorization.split(' ')
	if(a[0] !== 'Bearer')
	{
		reply.code(401).send({ message: 'Authorization header is not Bearer' })
		return
	}

	if(!a[1] || typeof a[1] !== 'string' || a[1].length < 10)
	{
		reply.code(401).send({ message: 'Must be logged in to perform this action' })
		return
	}

	const jwt = jwtVerify(a[1], secrets.jwtSecret)

	if(!jwt)
	{
		// reply.send({
		// 	message: 'The JWT is not valid',
		// 	// jwt, secrets.jwtSecret, a
		// })
		reply.code(401).send({
			message: 'Not logged in or login expired.',
			// jwt,
			// secrets.jwtSecret,
			// token: a,
		})
		return
	}

	req.user = jwt

}

// Allows any logged in user to access the route
function appAuthJWT(req, reply, done)
{
	authJwtPart(req, reply)
	done()
}

// Allows any `admin` role to access the route
function appAuthAdmin(req, reply, done)
{
	authJwtPart(req, reply)
	if(!req.user.role || req.user.role !== 'admin')
	{
		reply.code(401).send({ message: 'Must be an "admin" to perform this action' })
	}
	done()
}
// Allows any `operator` role to access the route
function appAuthOperator(req, reply, done)
{
	authJwtPart(req, reply)
	if(!req.user.role || !(req.user.role === 'admin' || req.user.role === 'operator'))
	{
		reply.code(401).send({ message: 'Must be an "operator" or "admin" to perform this action' })
	}
	done()
}

const AUTH_ANY      = { preHandler: appAuthJWT }
const AUTH_OPERATOR = { preHandler: appAuthOperator }
const AUTH_ADMIN    = { preHandler: appAuthAdmin }

module.exports = {
	appAuthBasic,
	appAuthJWT,
	appAuthAdmin,
	appAuthOperator,
	AUTH_ANY,
	AUTH_OPERATOR,
	AUTH_ADMIN,
}

