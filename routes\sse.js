
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// sse.js
//
// Server Sent Events
//=============================================================================================
/**
 * @module routes/info
 */

const { API_ROOT, mac } = require('../System.js')

/**
 * SSE client grouped by route name
 * Each group holds a Set of connected client responses
 * @type {Object.<string, {clients: Set<import('http').ServerResponse>}>}
 */
const SSEs = {
	poll: {
		clients: new Set()
	},
	datalog: {
		clients: new Set()
	}
}

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the info page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function sseRoutes(fastify, options, done) {
	fastify.get(API_ROOT + '/mac', (req, reply) => {
		reply.send({ mac })
	})

	// SSE endpoint for dynamic channels (e.g., /sse/poll, /sse/fine)
	// Connects clients to the specified event stream type, defaults to "poll" if invalid
	fastify.get(API_ROOT + '/sse/:type?', (request, reply) => {
		const { type } = request.params;
		const sseGroup = SSEs[type] ?? SSEs['poll']; // Default "poll" if invalid

		// Set headers for SSE
		reply.raw.setHeader('Access-Control-Allow-Origin', '*');
		reply.raw.setHeader('Content-Type', 'text/event-stream');
		reply.raw.setHeader('Cache-Control', 'no-cache');
		reply.raw.setHeader('Connection', 'keep-alive');
		reply.raw.flushHeaders();

		// Add client to set
		sseGroup.clients.add(reply.raw);

		// Remove client on error and disconnect
		const cleanup = () => {
			sseGroup.clients.delete(reply.raw);
			reply.raw.end();
		};
		request.raw.on('close', cleanup);
		request.raw.on('error', cleanup);
	});

	// SSE test endpoint with basic frontend
	fastify.get(API_ROOT + '/sse/test', async (request, reply) => {
		return reply.type('text/html').send(`
			<html>
				<body>
				<h1>Shared Interval SSE Example</h1>
				<div id="events"></div>
				<script>
					const eventSource = new EventSource('` + API_ROOT + `/sse');
					eventSource.onmessage = function(event) {
						console.log('SSE EventSource', event);
						const div = document.getElementById('events');
						div.innerHTML = '<p>' + event.data + '</p>';
					};
				</script>
				</body>
			</html>
		`);
	});

	done()
}

/**
 * Broadcasts a message to all SSE clients connected on a specific route.
 *
 * @param {string|Object} message - The message to send; if an object, it will be JSON-stringified.
 * @param {string} [route='poll'] - The SSE route to broadcast to.
 */
function sseBroadcast(message, route = 'poll') {
	const target = SSEs[route] ?? SSEs['poll'];
	const payload = typeof message === 'string' ? `data: ${message}\n\n` : `data: ${JSON.stringify(message)}\n\n`;
	for (const client of target.clients) {
		client.write(payload);
	}
}

/**
 * Closes all SSE client connections across all SSE groups.
 */
function sseClose() {
	for (const { clients } of Object.values(SSEs)) {
		for (const c of clients) c.end();
	}
}

module.exports = {
	sseRoutes,
	sseBroadcast,
	sseClose,
}