//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// ModbusFunctions.js
// 'use strict'

// const ModbusIoTypes = require('./ModbusIoTypes.js')
// import ModbusIoTypes from "./ModbusIoTypes.js"


//----------------------------------------------------------------------------------------------------
// ModbusIoTypesList: the list of standard Modbus IO types
// This doesn't include user defined types, these are loaded from JSON by the IoTypes.js file
//----------------------------------------------------------------------------------------------------
// const ModbusIoTypesList =  Object.keys(ModbusIoTypes).concat(['code'])

//====================================================================================================
// Modbus Function Codes
// 01 - Read one or more PLC output "coils" (1 bit each)
// 02 - Read one or more PLC input "contacts" (1 bit each)
// 03 - Read one or more PLC "holding" registers (16 bits each)
// 04 - Read one or more PLC analog input registers (16 bits each)
// 05 - Write (force) a single PLC output "coil" (1 bit)
// 06 - Write (preset) a single PLC "holding" register (16 bits)
// 15 - Write (force) multiple PLC output "coils" (1 bit each)
// 16 - Write (preset) multiple PLC "holding" registers (16 bits each)
//====================================================================================================

//====================================================================================================
// | Register Type     | Address Range (Decimal)  | Modbus Codes | Description           |
// |-------------------|--------------------------|--------------|-----------------------|
// | Coil              | 00001 - 09999            | 1, 5, 15     | Read/Write bits       |
// | Discrete Input    | 10001 - 19999            | 2            | Read-only bits        |
// | Input Register    | 30001 - 39999            | 4            | Read-only 16-bit data |
// | Holding Register  | 40001 - 49999            | 3, 6, 16     | Read/Write 16-bit data|
//====================================================================================================


/**
 * Converts a Modbus register notation (e.g., 30001, 40001) into parameters for use with modbus-serial.
 *
 * @param {number} reg - The Modbus register number to convert.
 *                        Valid ranges: 00001-09999, 10001-19999, 30001-39999, 40001-49999.
 *                        These correspond to:
 *                          - 0xxxx: Coils
 *                          - 1xxxx: Discrete Inputs
 *                          - 3xxxx: Input Registers
 *                          - 4xxxx: Holding Registers
 *
 * @returns {{address: number, fc: number, readFunction: string} | {error: string}}
 *          Returns an object containing:
 *            - address: Zero-based address for the register (number).
 *            - fc: Modbus function code (number).
 *            - readFunction: Function name used by modbus-serial (string).
 *          If the register is invalid, returns an error object.
 *
 * @example
 * ModbusRegisterToParams(30001);
 * // Returns: { address: 0, fc: 4, readFunction: 'readInputRegisters' }
 */
function ModbusRegisterToParams(reg)
{
	const eobj = {error: 'A modbus register must be a number in the range 00001-09999, 10001-19999, 30001-39999, or 40001-49999'}
	const address = (reg % 10000) - 1
	if(isNaN(address) || address < 0 || reg > 49999 || reg >= 20000 && reg < 30001)
	{
		return eobj
	}
	const c = parseInt(reg / 10000)
	let f = null
	let fc = 0
	// This is the function used by "modbus-serial"
	// See: https://github.com/yaacov/node-modbus-serial/wiki/Methods
	// readCoils(address, length)
	// readDiscreteInputs(address, length)
	// readHoldingRegisters(address, length)
	// readInputRegisters(address, length)
	switch(c)
	{
		case 0:
			f = 'readCoils'
			fc = 0x01
			break
		case 1:
			f = 'readDiscreteInputs'
			fc = 0x02
			break
		case 3:
			f = 'readInputRegisters'
			fc = 0x04
			break
		case 4:
			f = 'readHoldingRegisters'
			fc = 0x03
			break
	}
	if(!f)
	{
		return eobj
	}

	return { address, fc, readFunction: f }
}

//====================================================================================================
// ModbusFcToParams() - Converts a Modbus function code value to a method name supported by "modbus-serial"
// The input fc value may be either a number or a string
// The return value is a string which is the method name supported by "modbus-serial"
//====================================================================================================
function ModbusFcToParams(fcSrc)
{
	const eobj = {error: 'Supported numeric/string values are 1, "*coils*", 2, "*discrete*", 3, "*holding*", 4, "*input*"'}
	const fcStr = typeof fcSrc === 'string' ? fcSrc.trim().toLowerCase() : ''
	let f = null
	let fc = 0

	if(fcSrc === 0x01 || fcStr.includes('coils'))
	{
		f = 'readCoils'
		fc = 0x01
	}
	else if(fcSrc === 0x02 || fcStr.includes('discrete'))
	{
		f = 'readDiscreteInputs'
		fc = 0x02
	}
	else if(fcSrc === 0x03 || fcStr.includes('holding'))
	{
		f = 'readHoldingRegisters'
		fc = 0x03
	}
	else if(fcSrc === 0x04 || fcStr.includes('input'))
	{
		f = 'readInputRegisters'
		fc = 0x04
	}
	if(!f)
	{
		return eobj
	}

	return { fc, readFunction: f }
}

function ModbusRegisterToAddress(reg)
{
	return (reg % 10000) - 1
}

/**
 * Generates a Modbus-compatible buffer from a number based on the specified data type.
 * The buffer can be used with ModbusClient.writeRegisters().
 *
 * @param {number|string} num - The number to convert to a Modbus buffer. Can be a number or string representation.
 * @param {string} dType - The data type specifying how the number should be encoded in the buffer.
 *                         Supported types:
 *                         - 'F64': 64-bit floating point (Big Endian)
 *                         - 'F32': 32-bit floating point (Big Endian)
 *                         - 'S48': 48-bit signed integer (Big Endian)
 *                         - 'U48': 48-bit unsigned integer (Big Endian)
 *                         - 'S32': 32-bit signed integer (Big Endian)
 *                         - 'U32': 32-bit unsigned integer (Big Endian)
 *                         - 'S16': 16-bit signed integer (Big Endian)
 *                         - 'U16': 16-bit unsigned integer (Big Endian, default)
 * @returns {Buffer|null} The generated buffer containing the number in the specified format,
 *                        or null if the input is not a valid number.
 * @example
 * /// Returns a 2-byte buffer with the 16-bit unsigned representation of 42
 * NumberToModbusBuffer(42, 'U16');
 * @example
 * /// Returns a 4-byte buffer with the 32-bit float representation of 3.14
 * NumberToModbusBuffer(3.14, 'F32');
 */
function NumberToModbusBuffer(num, dType)
{
	const val = Number(num)

	if(isNaN(val)) return null

	let buf

	switch(dType.toUpperCase())
	{
		case 'F64':
			buf = Buffer.alloc(8)
			buf.writeDoubleBE(val)
			break

		case 'F32':
			buf = Buffer.alloc(4)
			buf.writeFloatBE(val)
			break

		case 'S48':
			buf = Buffer.alloc(6)
			buf.writeIntBE(val, 0, 6)
			break

		case 'U48':
			buf = Buffer.alloc(6)
			buf.writeUIntBE(val, 0, 6)
			break

		case 'S32':
			buf = Buffer.alloc(4)
			buf.writeInt32BE(val)
			break

		case 'U32':
			buf = Buffer.alloc(4)
			buf.writeUInt32BE(val)
			break

		case 'S16':
			buf = Buffer.alloc(2)
			buf.writeInt16BE(val)
			break

		default:
		case 'U16':
			buf = Buffer.alloc(2)
			buf.writeUInt16BE(val)
			break
	}
	return buf
}

// Parse a source params object to generate a cleaned up params object
// function ModbusParamsParse(src)
// {
// 	// The `register` parameter takes precedence over the `address` parameter
// 	let address
// 	let fc
// 	if(!isNaN(src.register))
// 	{
// 		const tmp = ModbusRegisterToParams(reg)
// 		address = tmp.address
// 		fc = tmp.fc
// 	}
// 	const srcFc = regGroup.params.fc
// 	if(typeof srcFc === 'string')
// 	{

// 	}
// 	// const fc = v || 3
// 	const unitid = regGroup.params.unitid || 1
// 	// const address
// 	const quantity = regGroup.params.quantity || 2
// 	const dst = {
// 		unitid,
// 		fc,
// 		address,
// 		quantity,
// 	}

// }

//====================================================================================================
// ModbusTypeValidate() - Validates the type of a Modbus register
//====================================================================================================


// export {
// 	ModbusRegisterToParams,
// 	ModbusReadFcParse,
// 	ModbusRegisterToAddress,
// 	// ModbusIoTypesList,
// }
// // export { ModbusIoTypesList }

// export default {
// 	ModbusRegisterToParams,
// 	ModbusReadFcParse,
// 	ModbusRegisterToAddress,
// 	// ModbusIoTypesList,
// }

module.exports = {
	ModbusRegisterToParams,
	ModbusRegisterToAddress,
	ModbusFcToParams,
	NumberToModbusBuffer,
	// ModbusIoTypesList,
}