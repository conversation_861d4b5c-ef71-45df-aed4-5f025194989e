//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// ModbusIoTypes.js
//
// Defines standard Modbus IO types and how to process them
//******************************************************************************************
// 'use strict'
console.log('========== ModbusIoTypes.js ========')

const { pointsToScale } = require('./Util.js')

//====================================================================================================
// Standard IO Types
//====================================================================================================

//----------------------------------------------------------------------------------------------------
// U16: Unsigned 16 bit
//----------------------------------------------------------------------------------------------------
const U16 = {
	parse: function(buffer, index = 0) { return buffer.readUInt16BE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'U16' },
}

//----------------------------------------------------------------------------------------------------
// S16: Signed 16 bit
//----------------------------------------------------------------------------------------------------
const S16 = {
	parse: function(buffer, index = 0) { return buffer.readInt16BE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'S16' },
}

//----------------------------------------------------------------------------------------------------
// U32: Unsigned 32 bit
//----------------------------------------------------------------------------------------------------
const U32 = {
	parse: function(buffer, index = 0) { return buffer.readUInt32BE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'U32' },
}

//----------------------------------------------------------------------------------------------------
// S32: Signed 32 bit
//----------------------------------------------------------------------------------------------------
const S32 = {
	parse: function(buffer, index = 0) { return buffer.readInt32BE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'S32' },
}
//----------------------------------------------------------------------------------------------------
// F32: Float 32 bit
//----------------------------------------------------------------------------------------------------
const F32 = {
	parse: function(buffer, index = 0) { return buffer.readFloatBE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'F32' },
}
//----------------------------------------------------------------------------------------------------
// F64: Float 64 bit (double)
//----------------------------------------------------------------------------------------------------
const F64 = {
	parse: function(buffer, index = 0) { return buffer.readDoubleBE(index) },
	init: function(src, dst, projectSettings) { dst.dType = 'F64' },
}



//====================================================================================================
// Specialized IO Types
//====================================================================================================


//====================================================================================================
//
// Calculate some scales/offset values for various sensors
//
// For the Brainchild IO-8AIIS-E we are using the 0-4096 range (type 1) -- NOT
// For the Brainchild IO-8AIIS-E we are using the 0–20.000mA 1uA (type 2)
//====================================================================================================
// const BrainchildCurrentLoopRange = 4095 // Mode 1
const BrainchildCurrentLoopRange = 20000 // Mode 2 is 20000 at 20mA
const BrainchildCurrentLoopRange0 = 4000 // Mode 2 is  4000 at 4mA

// clip any sensor value above this
// const BrainchildCurrentLoopRangeMax = (BrainchildCurrentLoopRange - BrainchildCurrentLoopRange0) * 1.2 + BrainchildCurrentLoopRange0
const BrainchildCurrentLoopRangeMax = BrainchildCurrentLoopRange + 10

const BrainchildCurrentLoopError = 2000  // Anything below this is a bad sensor

// Function to read a brainchild 4-20mA current loop device and detect a bad sensor (<2mA)
/**
 * Reads a Brainchild 4-20mA current loop device value from a buffer and detects sensor errors.
 *
 * @param {Buffer} buffer - The buffer containing the data.
 * @param {number} index - The offset (in bytes) from the start of the buffer.
 * @param {Object} [obj] - Optional object to store error status.
 * @param {boolean} [obj.error] - Will be set to `true` if the raw value indicates a bad sensor (< 2mA).
 * @returns {number} The raw current loop value read from the buffer.
 */
function readBrainchildCurrentLoop(buffer, index, obj) {
	const reg = buffer.readUInt16BE(index)
	const raw = Math.min(reg, BrainchildCurrentLoopRangeMax) // clip any sensor value above this
	if(typeof obj === 'object') obj.error = raw <= BrainchildCurrentLoopError
	return raw
}

// Same as above except a 4mA floor is applied (prevents negative pressure/flow values)
// function readBrainchildCurrentloopFloor(buffer, index, obj) {
// 	const raw = readBrainchildCurrentloop(buffer, index, obj)
// 	return raw //Math.max(raw, 4000)
// }

//----------------------------------------------------------------------------------------------------
// TemperatureSensorOnBrainchild: 4-20mA Temperature sensor on Brainchild IO-8AIIS-E set for 0-20000
//----------------------------------------------------------------------------------------------------
/**
 * Represents a temperature sensor connected to the Brainchild IO-8AIIS-E using a 4-20mA current loop.
 * Provides parsing and initialization logic for converting current loop values to temperature in °C.
 */
const TemperatureSensorOnBrainchild = {
	/**
	 * Parses the raw input from the current loop.
	 * @function
	 */
	parse: readBrainchildCurrentLoop,

	/**
	 * Initializes the temperature sensor scaling and units.
	 * @param {Object} src - The source register object (unused).
	 * @param {Object} dst - The sensor object to attach scaling data to.
	 * @param {Object} projectSettings - Settings object that may include `TS_MIN` and `TS_MAX` values.
	 * @param {number} [projectSettings.TS_MIN=-10] - Temperature at 4mA
	 * @param {number} [projectSettings.TS_MAX=40] - Temperature at 20mA
	 */
	init: function(src, dst, projectSettings) {
		const TS_MIN = projectSettings.TS_MIN == undefined ? -10 : projectSettings.TS_MIN
		const TS_MAX = projectSettings.TS_MAX == undefined ?  40 : projectSettings.TS_MAX
		const s = pointsToScale((4 / 20) * BrainchildCurrentLoopRange, TS_MIN, BrainchildCurrentLoopRange, TS_MAX)
		dst.scale = s.scale
		dst.offset = s.offset
		dst.units = '°C'
		dst.display = 2
		dst.dType = 'U16'
	}
}

//----------------------------------------------------------------------------------------------------
// PressureSensorOnBrainchild: 4-20mA Pressure sensor on Brainchild IO-8AIIS-E set for 0-20000
//----------------------------------------------------------------------------------------------------
/**
 * Represents a pressure sensor connected to the Brainchild IO-8AIIS-E using a 4-20mA current loop.
 * Provides parsing and initialization logic for converting current loop values to pressure in PSIG.
 */
const PressureSensorOnBrainchild = {
	/**
	 * Parses the raw input from the current loop.
	 * @function
	 */
	parse: readBrainchildCurrentLoop,

	/**
	 * Initializes the pressure sensor scaling and units.
	 * @param {Object} src - The source register object (unused).
	 * @param {Object} dst - The sensor object to attach scaling data to.
	 * @param {Object} projectSettings - Settings object that may include `PS_MAX` value.
	 * @param {number} [projectSettings.PS_MAX=100] - Pressure at 20mA
	 */
	init: function(src, dst, projectSettings) {
		const PS_MAX = projectSettings.PS_MAX == undefined ?   100 : projectSettings.PS_MAX
		const s = pointsToScale((4 / 20) * BrainchildCurrentLoopRange, 0, BrainchildCurrentLoopRange, PS_MAX)
		dst.scale = s.scale
		dst.offset = s.offset
		dst.units = 'PSIG'
		dst.display = 2
		dst.dType = 'U16'
	}
}

//----------------------------------------------------------------------------------------------------
// FlowSensorOnBrainchild: 4-20mA Flow sensor on Brainchild IO-8AIIS-E set for 0-20000
//----------------------------------------------------------------------------------------------------
/**
 * Represents a flow sensor connected to the Brainchild IO-8AIIS-E using a 4-20mA current loop.
 * Provides parsing and initialization logic for converting current loop values to flow in US GPM.
 */
const FlowSensorOnBrainchild = {
	/**
	 * Parses the raw input from the current loop.
	 * @function
	 */
	parse: readBrainchildCurrentLoop,

	/**
	 * Initializes the flow sensor scaling and units.
	 * @param {Object} src - The source register object (unused).
	 * @param {Object} dst - The sensor object to attach scaling data to.
	 * @param {Object} projectSettings - Settings object that may include `FS_MAX` value.
	 * @param {number} [projectSettings.FS_MAX=2000] - Maximum expected flow in US GPM.
	 */
	init: function(src, dst, projectSettings) {
		const FS_MAX = projectSettings.FS_MAX == undefined ?  2000 : projectSettings.FS_MAX
		const s = pointsToScale((4 / 20) * BrainchildCurrentLoopRange, 0, BrainchildCurrentLoopRange, FS_MAX)
		dst.scale = s.scale
		dst.offset = s.offset
		dst.units = 'US GPM'
		dst.display = 2
		dst.dType = 'U16'
	}
}

function DigitalInputOnBrainchild8DioBx(buffer, index = 0, mask = 0x01)
{
	return !!(buffer.readUInt16BE(index) & mask)
}

function DigitalInputOnBrainchild8DioInit(src, dst, projectSettings) {
	dst.scale = 1
	dst.offset = 0
	dst.units = ''
}

const DigitalInputOnBrainchild8DioB0 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x01) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB1 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x02) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB2 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x04) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB3 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x08) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB4 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x10) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB5 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x20) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB6 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x40) },
	init: DigitalInputOnBrainchild8DioInit
}
const DigitalInputOnBrainchild8DioB7 = {
	parse: function(buffer, index = 0) { return DigitalInputOnBrainchild8DioBx(buffer, index, 0x80) },
	init: DigitalInputOnBrainchild8DioInit
}

module.exports = {
	U16,
	S16,
	U32,
	S32,
	F32,
	F64,
	TemperatureSensorOnBrainchild,
	PressureSensorOnBrainchild,
	FlowSensorOnBrainchild,
	DigitalInputOnBrainchild8DioB0,
	DigitalInputOnBrainchild8DioB1,
	DigitalInputOnBrainchild8DioB2,
	DigitalInputOnBrainchild8DioB3,
	DigitalInputOnBrainchild8DioB4,
	DigitalInputOnBrainchild8DioB5,
	DigitalInputOnBrainchild8DioB6,
	DigitalInputOnBrainchild8DioB7,
}

