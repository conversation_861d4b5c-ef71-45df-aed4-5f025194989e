
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// routes/alarms.js
//
// <PERSON> routes
//=============================================================================================
console.log('========== routes/alarms.js ========')

/**
 * @module routes/alarms
 */

const { API_ROOT } = require('../System.js')
const { alarms } = require('../Alarm.js')
const { objectExclude, stringToBoolean } = require('../Util.js')
const { AUTH_OPERATOR } = require('../AppLoginAuth.js')

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the alarms page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function alarmsRoutes(fastify, options, done)
{
	// Register state route
	fastify.get(API_ROOT + '/status/alarms', async (request, reply) => {
		return reply.send(alarms)
	})


	fastify.delete(API_ROOT + '/status/lockouts', AUTH_OPERATOR, async (request, reply) => {
		alarms.forEach(alarm => {
			let n = 0
			n += alarm.clearLockout()
		})
		return reply.send({ message: n + 'Lockouts and Alarms cleared' })
	})
	fastify.delete(API_ROOT + '/status/alarms', async (request, reply) => {
		let n = 0
		alarms.forEach(alarm => {
			n += alarm.clear()
		})
		return reply.send({ message: n + ' Alarms cleared' })
	})



	done()
}


module.exports = alarmsRoutes
