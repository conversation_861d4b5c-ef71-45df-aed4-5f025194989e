//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// export/Alarm.js
//
// An Alarm constructor class
//
//=============================================================================================
console.log('========== Alarm.js ================')
const { parseDuration } = require('./Util.js')
const { timestamp }     = require('./Global.js')

const alarms = [] // Array of all alarms

/**
 * Represents an Alarm with configurable properties and state tracking
 *
 * @class Alarm
 * @param {Object} options - Configuration options for the alarm
 * @param {string} [options.name='Alarm{index}'] - Name of the alarm
 * @param {number} [options.setpoint=0] - Threshold value for alarm triggering
 * @param {string} [options.compare='>'] - Comparison operator for triggering
 * @param {boolean} [options.lockout=false] - Whether the alarm is a lockout type
 * @param {string} [options.comment=''] - Additional comment for the alarm
 * @param {string} [options.units=''] - Units of the setpoint value
 * @param {number} [options.display=0] - Decimal places for display
 * @param {number} [options.duration=60] - Duration for alarm trigger in seconds
 */
class Alarm {
	/**
 	 * Initializes a new Alarm instance with specified configuration options
 	 *
 	 * @param {Object} options - Configuration options for the alarm
 	 * @param {string} [options.name='Alarm{index}'] - Name of the alarm, defaults to 'Alarm' + current alarm count
 	 * @param {number} [options.setpoint=0] - Threshold value for alarm triggering
 	 * @param {string} [options.compare='>'] - Comparison operator for triggering (e.g., '>', '<', '==')
 	 * @param {boolean} [options.lockout=false] - Whether the alarm is a lockout type
 	 * @param {string} [options.comment=''] - Additional comment for the alarm
 	 * @param {string} [options.units=''] - Units of the setpoint value
 	 * @param {number} [options.display=0] - Decimal places for display
 	 * @param {number} [options.duration=60] - Duration for alarm trigger in seconds
 	 */
	constructor(options) {
		// These are set in the constructor and remain constant
		this.name     = options.name     || 'Alarm' + alarms.length  // Alarm Name
		this.setpoint = options.setpoint || 0     // The setpoint value
		this.compare  = options.compare  || '>'   // The comparison operator
		this.lockout  = options.lockout  || false // If the Alarm is a lockout
		this.comment  = options.comment  || ''    // A comment to be displayed with the Alarm
		this.units    = options.units    || ''    // The units of the setpoint value
		this.display  = options.display  || 0     // The display decimal places
		this.duration = parseDuration(options.duration || 60)    // The trigger duration of the event in seconds
		// These are set in real time
		this.alarm    = false // The alarm state (true = alarm has been triggered)
		this.active   = false // The condition causing the alarm is active
		this.value    = null  // The current value being compared
		this.start    = 0
		this.error    = false
		alarms.push(this)
	}

	/**
 	 * Clears the lockout state of the alarm
 	 *
 	 * @returns {number} 1 if the alarm was previously triggered, 0 otherwise
 	 */
	clearLockout() {
		const r = this.alarm ? 1 : 0
		this.alarm  = false
		this.active = false
		this.start  = 0
		return r
	}

	/**
	 * Clears the alarm if it is not a lockout type
	 *
	 * @returns {number} 1 if the alarm was previously triggered and cleared, 0 otherwise
	 */
	clear() {
		if(!this.lockout)
		{
			return this.clearLockout()
		}
		return 0
	}
	service(value, opt) {
		if(typeof opt === 'object')
		{
			Object.assign(this, opt)
		}
		if(typeof value === 'object')
		{
			this.value = value.value
			this.error = value.error
			if(value.error)
			{
				this.active = false
				this.start = 0
				return
			}
		}
		else
		{
			this.value = value
		}

		let cmp = false
		switch(this.compare)
		{
			case '<':
				cmp = this.value < this.setpoint
				break

			case '>':
				cmp = this.value > this.setpoint
				break

			case '<=':
				cmp = this.value <= this.setpoint
				break

			case '>=':
				cmp = this.value >= this.setpoint
				break

			case '=':
			case '==':
				cmp = this.value == this.setpoint
				break

			default:
				break
		}


		this.timestamp = timestamp.s

		if(cmp) // Becomes active
		{
			if(this.active)
			{
				if(this.start > 0 && (this.timestamp - this.start) > this.duration)
				{
					this.alarm = true
				}
			}
			else
			{
				this.start = this.timestamp
			}
		}
		else
		{
			this.start = 0
		}

		this.active = cmp

	}
}

// Generate the email body for the alert email and send it to the email address
function AlertEmail()
{

}

function Svc()
{

}

module.exports = {
	Svc,
	Alarm,
	alarms
}
