//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Persist.js
//
// Deal with persistent data
//=============================================================================================
console.log('========== Persist.js ==============')
const fs = require('fs')

const registersPersist = {
	filename: '/public/data/persist.json',  // Persistent registers will be stored here
	registers: [
		{ device: 'virtual', name: 'HeatTransferIn' },
		{ device: 'virtual', name: 'HeatTransferInFS01' },
		{ device: 'virtual', name: 'HeatTransferOut' },
		{ device: 'virtual', name: 'HeatTransferOutFS01' },
		{ device: 'virtual', name: 'PumpSelect1' },
		{ device: 'virtual', name: 'PumpSelect2' },
	],
	// The save function will store all of the persistent registers into the file as JSON
	save: function (registers) {
		const pregs = {}
		let r = 'Error'
		let i = 0
		try {
			this.registers.forEach(reg => {
				if (pregs[reg.device] === undefined) pregs[reg.device] = {};

				pregs[reg.device][reg.register] = registers[reg.device][reg.register].value
				i++
			})
			fs.writeFileSync(this.filename, JSON.stringify(pregs))
			r = 'Registers written: ' + i
		} catch (error) {
			let r = 'Error in reg ' + i
		}
		return r
	},
	load: function (glob) {
		let nPersist = 0
		let p = null

		try {
			p = JSON.parse(fs.readFileSync(this.filename))
		} catch (error) { }

		if (p) {
			this.registers.forEach(reg => {
				try {
					if (p[reg.device] && p[reg.device][reg.register] != undefined) {
						glob[reg.device][reg.register].value = p[reg.device][reg.register]
						nPersist++
					}
					else if (glob[reg.device] && glob[reg.device][reg.register] && glob[reg.device][reg.register].defaultValue != undefined) {
						glob[reg.device][reg.register].value = glob[reg.device][reg.register].defaultValue
					}
				} catch (error) { }
			})
		}
		return nPersist
	}
}


module.exports = {
	read,
	write,
}
