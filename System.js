//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// System.js
// Handle the system defined variables
// /**
//  * @module System
//  */
"use strict"

console.log('========== System.js ===============')

const path = require("path")
const fs = require("fs")
const { uid } = require('uid/secure') // Use uid to generate a secure random string
// const { type } = require("os")
const bcrypt = require("bcryptjs")
const { getSystemMacAddress } = require('./Util.js')

// const salt = bcrypt.genSaltSync(10)

/**
 * @type {UserObject}
 */
const DEFAULT_USER1 = {
	user: 'admin',
	pass: bcrypt.hashSync('praevista', bcrypt.genSaltSync(10)),
	role: 'admin',
}

const DEFAULT_USER2 = {
	user: 'operator',
	pass: bcrypt.hashSync('praevista', bcrypt.genSaltSync(10)),
	role: 'operator',
}

const DEFAULT_USER3 = {
	user: 'mike',
	pass: '$2b$10$Muxtnxohv9wvfZcYsQgyu.egZX5eJKQkWOoceIwRGnKTdGLjzrwf.',
	role: 'admin',
}


// console.log('///==>> bcrypt.compareSync()', bcrypt.compareSync('praevista', DEFAULT_USER.password))

const DEFAULT_USERS = [DEFAULT_USER1, DEFAULT_USER2, DEFAULT_USER3]

const users = JSON.parse(JSON.stringify(DEFAULT_USERS))

const API_ROOT = '/api'
const SYSTEM_DIRECTORY = '/public/system' // path.resolve('system') // + path.sep

const JWT_SECRET_FILE    = SYSTEM_DIRECTORY + path.sep + 'jwt-secret.conf'
const API_KEY_FILE       = SYSTEM_DIRECTORY + path.sep + 'api-key.conf'
const USERS_FILE         = SYSTEM_DIRECTORY + path.sep + 'users.json'

function checkSystemDirectory()
{
	// Check if the directory exists
	if (!fs.existsSync(SYSTEM_DIRECTORY))
	{
		// Create the directory
		fs.mkdirSync(SYSTEM_DIRECTORY)
		console.log('System config directory being created:', SYSTEM_DIRECTORY)
	}
}

function newJwtSecretFile(filePath)
{
	try {
		// Check if the directory exists
		checkSystemDirectory()
		const secret = uid(32)
		fs.writeFileSync(filePath, JSON.stringify(secret))
		return secret
	} catch (err) {
		console.error('Problem with secret creation, must exit', err)
		process.exit(1)
	}
}

function newUsersFile()
{
	try {
		checkSystemDirectory()
		const users = JSON.parse(JSON.stringify(DEFAULT_USERS))
		fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, '\t'))
		return users
	} catch (err) {
		console.error('Fatal system problem accessing:', USERS_FILE, err)
		process.exit(2)
	}
}

//--------------------------------------------------------------------------------------
// setUsers() - Set the in-memory users array
//--------------------------------------------------------------------------------------
	function setUsers(newUsers)
{
	let u = []
	if(Array.isArray(newUsers))
	{
		u = newUsers
	}
	users.length = 0
	users.push(...u)
}


//--------------------------------------------------------------------------------------
/**
 * Return the users array from the stored file, including passwords
 * - If the file does not exist, return the default user
 * @returns {UserObject} array of users
 */
//--------------------------------------------------------------------------------------
function getUsersFile()
{
	try {
		return JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
	} catch (error) {
	}
	return [DEFAULT_USER1, DEFAULT_USER2, DEFAULT_USER3]
}

//------------------------------------------------------------------------------------------
// System MAC address (also used as serial number)
//------------------------------------------------------------------------------------------
const mac = getSystemMacAddress() //getMAC().toUpperCase()

const secrets = {
	apiKey: null,
	jwtSecret: null,
}

//==========================================================================================
// System.init()
//==========================================================================================
function init()
{
	//--------------------------------------------------------------------------------------
	// Load or create our JWT secret
	//--------------------------------------------------------------------------------------
	try {
		secrets.jwtSecret = JSON.parse(fs.readFileSync(JWT_SECRET_FILE, 'utf8'))
		console.log('+++++>>> JWT secret file loaded:', secrets.jwtSecret)
	} catch (err) {
		secrets.jwtSecret = newJwtSecretFile(JWT_SECRET_FILE)
		console.log('+++++>>> JWT secret file being created:', secrets.jwtSecret)
	}

	//--------------------------------------------------------------------------------------
	// Load or create our API key
	//--------------------------------------------------------------------------------------
	try {
		secrets.apiKey = JSON.parse(fs.readFileSync(API_KEY_FILE, 'utf8'))
	} catch (err) {
		secrets.apiKey = newJwtSecretFile(API_KEY_FILE)
		console.log('API kyey file being created:', secrets.apiKey)
	}

	//--------------------------------------------------------------------------------------
	// Load or create our user/password file
	//--------------------------------------------------------------------------------------

	let newUsers = null
	try {
		newUsers = JSON.parse(fs.readFileSync(USERS_FILE, 'utf8'))
	} catch (err) {
		newUsers = newUsersFile()
		console.log('==> Users file being created with the default login')
	}
	setUsers(newUsers)
	// console.log('==> Users:', users)

}




// const n = CONFIG_DIRECTORY.length
// const ConfigFileList = (() =>{
// 	const val = {}
// 	Object.keys(files).forEach( (file) => {
// 		val[files[file].substring(n)] = files[file]
// 	})
// 	return val
// })()





module.exports = {
	users,
	getUsersFile,
	init,
	API_ROOT,
	SYSTEM_DIRECTORY,
	JWT_SECRET_FILE,
	// jwtSecret,
	secrets,
	mac,
}
