// import DatalogHandler from '../DatalogHandler.cjs'
import metadata from '../data/metadata.json' with { type: 'json' }
import { postMetadata, postDataBatch } from './lib/post_functions.js'
import { error } from 'console'
import { processHistoricalData } from './lib/csv_helper.js'

/**
 * @typedef {string} CurrentState
 * values
    IDLE 
    METADATA_START
    METADATA_IN_PROGRESS
    CSV_START
    CSV_IN_PROGRESS
    LIVE_STREAM_START
    LIVE_STREAM_IN_PROGRESS
    ERROR 
    SHUTDOWN_START
    SHUTDOWN_PROGRESS
 */
/** @type {CurrentState | null} */
let currentState = null

let currentPromise = null
let MAX_LIVE_DATA_QUEUE_SIZE = 1000

let latestTimestamp = null // acts as t1 
let dataTime = null // latest device timestamp recieved by server, acts as t0


/**
 * @type {Array} liveDataQueue
 */
const liveDataQueue = []

// configuration - passed from embedded system
let config = {
    serverUrl: null,
    batchSize: 100,
    projectFinePath: null,
    projectMetadataJsonPath: null,
    datalogResolution: "fine",
    postFormat: "array"
};

const controller = new AbortController()
const { signal } = controller
/**
 * Initializes the data poster module.
 * Sets up configuration, loads metadata, and prepares the state machine.
 *
 * @param {object} projectSettings - Configuration settings passed from the embedded system.
 * @param {string} projectSettings.fine - Path to the directory containing CSV files.
 * @param {string} projectSettings.metadata - Path to the metadata JSON file.
 * @param {string} projectSettings.serverUrl - URL of the data server.
 */
function init(projectSettings) {
    console.log('[INITT] Starting initialization...')

    config.serverUrl = projectSettings.serverUrl
    config.projectFinePath = projectSettings.fine
    config.projectMetadataJsonPath = projectSettings.metadata


    try {
        if (metadata) {
            console.log('[INIT] Device metadata loaded successfully')
        } else {
            console.log('[INIT] Metadata empty...')
        }
    } catch (error) {
        console.error('[INITT] Device metadata not loaded', error)
        currentState = "ERROR"
        return
    }

    liveDataQueue.length = 0

    currentState = "IDLE"

    console.log(`[INITT] Initial state set to: ${currentState}`)
    console.log('[INITT] Initialization complete')
}


/**
 *
 * @param {object} data - Data from the embedded system.
 */
function svc(data) {


    // passed data to fifo 
    if (data) {
        liveDataQueue.push(data)
        latestTimestamp = data[0]
        if (liveDataQueue.length > MAX_LIVE_DATA_QUEUE_SIZE) {
            console.warn(`[SVC] Live data queue overflow! Size: ${liveDataQueue.length}`)
            liveDataQueue.length = 0 
            currentState = "IDLE"
            return
        }
    }
    console.log(`[SVC] Current state: ${currentState}, Live Queue: ${liveDataQueue.length}`)

    switch (currentState) {
        case "IDLE":

        case "METADATA_START":
            console.log("[SVC] Initiating metadata send")
            currentState = "METADATA_IN_PROGRESS"
            currentPromise = postMetadata(config.serverUrl, metadata, signal)

                .then((data) => data.response.text())

                .then((data) => {
                    console.log("[SVC] Metadata successfully sent")
                    dataTime = Number(data.split(',')[1])
                    console.log(`[SVC] Server response dataTime: ${dataTime}`)

                    currentState = "CSV_IN_PROGRESS"
                    console.log(`[SVC] State: ${currentState}. Starting historical data processing...`)

                    return processHistoricalData(dataTime, latestTimestamp, {
                        ...config,
                        mac: metadata.mac,
                        signal: signal
                    })
                })
                .then(() => {
                    console.log(`[SVC] CSV data processed successfully`)
                    currentState = "LIVE_STREAM_START"
                    console.log(`[SVC] State changed to: ${currentState}`)
                })
                .catch((error) => {
                    console.error(`[SVC] Error : ${error.message}`)
                    currentState = "ERROR"
                })
            break

        case "METADATA_IN_PROGRESS":
            break

        case "CSV_START":
            break

        case "CSV_IN_PROGRESS":
            console.log("[SVC] CSV processing ongoing, awaiting completion");
            break

        case "LIVE_STREAM_START":
            if (liveDataQueue.length > 0) {
                currentState = "LIVE_STREAM_IN_PROGRESS"
                console.log(`[SVC] State: ${currentState}. Queue size: ${liveDataQueue.length}`)

                const dataToStream = [...liveDataQueue] // pushes all data from livedataqueue, so accumulated points flushed on first call
                currentPromise = postDataBatch(dataToStream, config.serverUrl, metadata.mac, signal) // dataToStream pushed to server 

                    .then(() => {
                        liveDataQueue.splice(0, dataToStream.length)
                        currentState = "LIVE_STREAM_START"
                    })
                    .catch(() => {
                        console.error(`[SVC] Error streaming live data: ${error.message}`)
                        currentState = "ERROR"
                    })
            }
            break

        case "LIVE_STREAM_IN_PROGRESS":
            break


        case "ERROR":
            console.error("[SVC] State: ERROR")
            currentState = "IDLE"
            break

        case "SHUTTING_DOWN":
            console.log("[SVC] State: SHUTTING_DOWN")
            break

        default:
            console.warn(`[SVC] Encountered unknown state: ${currentState}`)
            currentState = "ERROR"
            break

    }
}

/**
 * Initiates the shutdown process, cleaning up resources and preparing for application termination.
 */
async function shutdown() {
    console.log("[SHUTDOWN] Starting shutdown...")
    currentState = "SHUTTING_DOWN"

    liveDataQueue.length = 0

    try {
        if (currentPromise) {
            console.log("[DEBUG] executing abort")
            controller.abort()
            await currentPromise
        }

    } catch (error) {
        console.error(`[SHUTDOWN] Error during shutdown: ${error.message}`)
    }

    console.log("[SHUTDOWN] SHUTTING_DOWN... Cleanup complete")

}


export default { init, svc, shutdown }