//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// This file grabs the Project specific JS
//
//=============================================================================================
console.log('========== Project.js ==============')

// This is the main entry point into the project specific JS
const ProjectIndex = require('../project/ProjectIndex.js')

// ProjectIndex should be composed of:
// {
// 	ProjectDefinitions,
// 	IoServersArray,
// 	IoServersArrayDev,
// 	RegistersList,
// 	RegistersPersist,
// 	Dashboards,
// 	UnitsConversions,
// 	Trends,
// }

// Decorate UnitsConversions.alternateUnits with scale and offset
function init(index)
{
	const alt = index.UnitsConversions.alternateUnits
	Object.keys(alt).forEach(key => {
		// Calculate scale and offset
		const y1 = alt[key].transform(1)
		const y0 = alt[key].transform(0)
		alt[key].scale = y1 - y0
		alt[key].offset = y0
	})

	return index
}

module.exports = init(ProjectIndex)
