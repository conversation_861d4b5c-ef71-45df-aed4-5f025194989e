//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// DocsInit.js
//
// Load the schedules.json JSON file and initialize in Global
//
//=============================================================================================
console.log('========== DocsInit.js =============')
const fs = require('fs')
const { documents }          = require('./Global.js')
const { ProjectDefinitions } = require('./Project.js')
const docsIndexFile = ProjectDefinitions.docsIndexFile

function parse(docsIndexText)
{
	const parsed = typeof docsIndexText == 'string' ? JSON.parse(docsIndexText) : docsIndexText
	return { parsed, errors: null }
}


function init()
{
	// Try to load the file
	try {
		fileStr = fs.readFileSync(docsIndexFile, 'utf8')
		const { parsed: docsArray, errors } = parse(fileStr)

		if (errors) {
			console.error('Error parsing ' + docsIndexFile, errors)
			return
		}

		documents.length = 0

		documents.push(...docsArray)

		// console.log('DocsInit loaded:', docsIndexFile, docsArray)
	} catch (err) {
		console.error('Error loading ' + docsIndexFile, err)
	}
	return documents
}

module.exports = {
	parse,
	init,
}