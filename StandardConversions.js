//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// StandardConversions.js
//
// Functions to convert between units
// For an enhanced way to convert units, see:
// https://chatgpt.com/c/67f533c6-f9bc-800b-bcb1-e2aa6321702d
// https://chatgpt.com/share/67f54079-74a8-800b-99de-78bf0fdf5b2b
//=============================================================================================
console.log('========== StandardConversions.js ==')

function degCtoDegF(c)
{
	return c * 1.8 + 32
}

function degFtoDegC(f)
{
	return (f - 32) / 1.8
}

// Source: https://www.convertunits.com/from/Btu/h/to/kW
function BtuPerHrToKw(val)
{
	return val * 0.00029307107
}
function KwToBtuPerHr(val)
{
	return val * 3412.1416351331
}

// Source: https://www.convertunits.com/from/Btu/to/kWh
function BtuTokWh(val)
{
	return val * 0.00029307108333333
}

// Source: https://www.kylesconverter.com/flow/gallons-(us-fluid)-per-minute-to-cubic-feet-per-hour
function UsGpmToFt3perHr(val)
{
	return val * 8.020833333333334
}

function kWtoHP(val)
{
	return val * 1.3596216173
}

// Source: https://www.unitjuggler.com/convert-flowrate-from-us-gpm-to-lmin.html
function USGPMtoLPM(val)
{
	return val * 3.7854
}

// Source: https://www.unitjuggler.com/convert-pressure-from-psi-to-kPa.html
function PSIGtoKPa(val)
{
	return val * 6.************
}

module.exports = {
	degCtoDegF,
	degFtoDegC,
	BtuPerHrToKw,
	KwToBtuPerHr,
	BtuTokWh,
	UsGpmToFt3perHr,
	kWtoHP,
	USGPMtoLPM,
	PSIGtoKPa,
}
