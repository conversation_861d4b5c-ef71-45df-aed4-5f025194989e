//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// IoRegistersInit.js
//
// Initialize the register structure (the one that will be live updated)
//=============================================================================================
console.log('========== IoRegistersInit.js ======')

const { objectExclude, objectInclude, numberWithCommas } = require('./Util.js')

// The project specific Unit Conversions
const { UnitsConversions } = require('./Project.js')
const alternateUnits = UnitsConversions.alternateUnits
const { applyIoType } = require('./IoTypes.js')
const { ModbusRegisterToParams, ModbusFcToParams, NumberToModbusBuffer } = require('./ModbusFunctions.js')

// The value as text in the native units
function asText(noUnits)
{
	let r
	const val = this._value
	if (typeof val === 'boolean')
	{
		r = val ? 'true' : 'false'
	}
	else if(val === null)
	{
		r = 'null'
	}
	else if(isNaN(val))
	{
		r = val
	}
	else
	{
		r = numberWithCommas(val, this.display) // + ' *' + this.display
	}

	if (!noUnits && this.units)
	{
		r += ' ' + this.units
	}
	return r
}

// value as text in the alternate units
function asTextAlternate()  {
	let r = ''
	const au = this.alt ? alternateUnits[this.units || ' na '] : false
	if(au)
	{
		const val = au.transform(this._value)

		if (isNaN(val))
		{
			r = val
		}
		else
		{
			r = numberWithCommas(val, this.display)
		}

		if(au.units)
		{
			r += ' ' + au.units
		}
	}
	return r
}
// item.unitId, item.startAddress, item.dataBuffer
function modbusWrite(value)
{
	const writeReg = this
	const readReg  = this._inputRegister
	if(value !== undefined)
	{
		writeReg.value = value
	}
	// Deadband support
	if(!this.ioServer || Math.abs(writeReg.raw - readReg.raw) > writeReg.deadband )
	{
		const dataBuffer = NumberToModbusBuffer(writeReg.raw, writeReg.dType)
		if(dataBuffer === null)
		{
			console.log('###>>> modbusWrite buffer write failed on', writeReg.name, writeReg.device)
			return
		}
		const opt =  {
			// unitId: this.ioServer,
			unitId: this.ioServer.uid || 1,
			startAddress: writeReg.address,
			dataBuffer,
		}
		// console.log('###>>> writeReg.raw', writeReg.raw, 'readReg.raw', readReg.raw, 'writeReg.deadband', writeReg.deadband)
		console.log('###>>> this.ioServer.modbusClient.writeRegister(',opt, ')')

		if(this.ioServer.writeQueue.length < 10)
		{
			this.ioServer.writeQueue.push(opt)
		}

		// console.log('###>>> modbusWrite',{
		// 	writeReg_name: writeReg.name,
		// 	writeReg_device: writeReg.device,
		// 	writeReg_dType: writeReg.dType,
		// 	writeReg_value: writeReg._value,
		// 	writeReg_raw: writeReg.raw,
		// 	readReg_raw: readReg.raw,
		// 	deadband: writeReg.deadband,
		// 	ioServer_name: this.ioServer.name,
		// 	ioServer_uid: this.ioServer.writeQueue,
		// 	// ioServer_modbusClient: this.ioServer.modbusClient,
		// })
	}
	// else
	// {
	// 	// console.log('###>>> modbusWrite', writeReg.raw, readReg.raw, typeof this.ioServer)
	// 	console.log('###>>> modbusWrite', this)
	// }
	// console.log(this)
}

// CHANGE: Add functions: asTextMetric and asTextImperial

/**
 * Initializes the register structure (the one that will be live updated)
 * @function init
 * @description Initializes the register structure (the one that will be live updated)
 * @param {object} registerList - The array of register sets
 * @param {object} ioServers - The array of ioServers (before processing)
 * @param {object} projectSettings - The project settings object
 * @returns {void}
 */
function init(registerList, ioServers, projectSettings)
{

	const updated = Date.now()
	const registersNew = {}
	const registerListClean = [] // The cleaned  up register list
	let n = 0

	//-----------------------------------------------------------------------------------------
	// Loop over the array of register sets
	//-----------------------------------------------------------------------------------------
	registerList.forEach(function(regGroup, iRegGroup) {
		// Copy over all properties except registers
		const newGroup = objectExclude(regGroup, ['registers', 'params', 'update'])
		newGroup.registers = []
		newGroup.updated = 0

		// const { registers, params, server } = regGroup
		const virtual = !regGroup.params || !regGroup.server // The register set are not from physical IO such as Modbus

		//-------------------------------------------------------------------------------------
		// `server`
		//-------------------------------------------------------------------------------------
		let myIoServer = null
		if(!virtual)
		{
			myIoServer = ioServers[regGroup.server]
			if(!myIoServer)
			{
				console.log('Server not found: ' + newGroup.server)
				return
			}

			//---------------------------------------------------------------------------------
			// `params`
			//
			// Normalize and parse the `params` object to make sure it is valid
			//---------------------------------------------------------------------------------
			// `register` takes priority over `fc` and `address`
			let pr = ModbusRegisterToParams(regGroup.params.register)
			if(pr.error)
			{
				// Detect as `fc` and `address`
				pr = ModbusFcToParams(regGroup.params.fc)
				if(!pr.error)
				{
					pr.address = regGroup.params.address || 0
				}
				else
				{
					console.log('IoRegistersInit: error in register params: ' + pr.error)
					return
				}
			}
			const unitid = regGroup.params.unitid || ioServers[newGroup.server].unitid || 1
			const quantity = regGroup.params.quantity || 2
			newGroup.params = {
				unitid,
				fc: pr.fc,
				address: pr.address,
				quantity,
			}
			//---------------------------------------------------------------------------------
			// `update`
			//
			// Controls how frequently the register set is to be updated via Modbus
			// Update frequency is in seconds
			// If the parameter is a number, then it is the update interval is that many
			// seconds on a modulo boundary of 0 (timeSeconds % update === 0).
			// If the parameter is an array of 2 numbers, then the first number is the update
			// interval in seconds and the second number is the modulo boundary.
			// (timeSeconds % update[0] === update[1])
			//---------------------------------------------------------------------------------
			if(!isNaN(regGroup.update))
			{
				newGroup.update = [parseInt(regGroup.update), 0]
			}
			else if(Array.isArray(regGroup.update)
				&& regGroup.update.length === 2
				&& !isNaN(regGroup.update[0])
				&& !isNaN(regGroup.update[1]))
			{
				newGroup.update = [parseInt(regGroup.update[0]), parseInt(regGroup.update[1])]
			}
		}


/*
		if(!virtual) // Track modbus communication
		{
			modbusComStats[regGroup.server] = {
				count: 0,   // Total number of sucessful transfers
				errors: 0,  // Total number of errors
				error: false, // see if the current transfer is an error
				errorTime: null,
				updated: 0,
			}
		}
*/
		//-----------------------------------------------------------------------------------------
		// Loop over the inner registers array
		//-----------------------------------------------------------------------------------------
		regGroup.registers.forEach(function(reg, iReg) {

			const regWrite = !!reg.write


			// The set/get for newReg is a little different for write registers
			const newReg = regWrite ? {
				get value() {
					return this._value
				},
				set value(v) {
					// console.log('regWrite set value: ' + v)
					this._value = v
					this.updated = Date.now()
					this.raw = this.noScale ? v : Math.floor((v - this.offset) / this.scale)
				},
				TMP: 'TMP',
			} : {
				get value() {
					return this._value
				},
				set value(v) {
					this._value = v
					this.updated = Date.now()
				}
			}



			Object.assign(newReg, {
				name: reg.name,
				device: reg.device,
				updated: 0,
				_value: reg.defaultValue || 0,
				display: isNaN(reg.display) ? null : parseInt(reg.display),
			})

			Object.assign(newReg, objectInclude(reg, ['comment', 'units']))

			// const newRegAdd = objectInclude(reg, ['name', 'comment', 'units', 'device'])

			//-------------------------------------------------------------------------------------
			// `alt` - Shall we display alternate values?
			//-------------------------------------------------------------------------------------
			newReg.alt = reg.alt === undefined ? true : !!reg.alt
			//-------------------------------------------------------------------------------------
			// `device`
			//-------------------------------------------------------------------------------------
			if(typeof newReg.device !== 'string')
			{
				newReg.device = newGroup.device
			}

			if(!newReg.device)
			{
				console.log('Device is required')
				return
			}
			const device = newReg.device

			//-------------------------------------------------------------------------------------
			// `name`
			//-------------------------------------------------------------------------------------
			if(typeof reg.name !== 'string')
			{
				console.log('A register "name" is required for group', iRegGroup, 'register', iReg, 'for device', device)
				return
			}

			//-------------------------------------------------------------------------------------
			// Make sure the devices object exists, create it if not
			//-------------------------------------------------------------------------------------
			if (typeof registersNew[device] !== 'object')
			{
				registersNew[device] = {}
			}

			//-------------------------------------------------------------------------------------
			// `scale` and `offset`
			//-------------------------------------------------------------------------------------
			newReg.scale = (isNaN(reg.scale) ? 1 : Number(reg.scale)) || 1
			newReg.offset = (isNaN(reg.offset) ? 0 : Number(reg.offset)) || 0
			newReg.noScale = newReg.scale === 1 && newReg.offset === 0

			//-------------------------------------------------------------------------------------
			// `write` Writable registers
			//-------------------------------------------------------------------------------------
			newReg.write = regWrite ? modbusWrite : null
			if(regWrite)
			{
				// newReg.server = newGroup.server
				newReg.ioServer = myIoServer
				if(typeof reg.inputRegister === 'string')
				{
					newReg.inputRegister = reg.inputRegister
					newReg.inputDevice   = reg.inputDevice || newReg.device
					newReg.deadband      = reg.deadband || 0
				}
			}

			//-------------------------------------------------------------------------------------
			// `type`
			//-------------------------------------------------------------------------------------
			if(!virtual)
			{
				const typeErr = applyIoType(reg, newReg, projectSettings)
				if(typeErr)
				{
					console.log('IoRegistersInit Error: group', iRegGroup, 'device', device, 'register', iReg, newReg.name, typeErr)

				}
			}

			//-------------------------------------------------------------------------------------
			// `value`, `updated`, `asText`, `asTextAlternate`, `display`
			//-------------------------------------------------------------------------------------
			Object.assign(newReg, {
				asText:          reg.showAlt ? asTextAlternate : asText,
				asTextAlternate: reg.showAlt ? asText : asTextAlternate,
				asTextNative:    asText,
				error: false, // used for current loop errors
			})

			// newReg.display = reg.display // isNaN(reg.display) ? null : parseInt(reg.display)

			//-------------------------------------------------------------------------------------
			// `index` needed for parsing the Modbus response, or for Modbus writes
			//-------------------------------------------------------------------------------------
			if(!virtual)
			{
				// index has highest priority
				if(!isNaN(reg.index))
				{
					newReg.index = parseInt(reg.index)
				}
				else
				{
					// `register` takes priority over `address`
					let pr = ModbusRegisterToParams(reg.register)
					if(pr.address) // we have a valid register
					{
						newReg.index = (pr.address - newGroup.params.address) * 2
					}
					else if(!isNaN(reg.address)) // we have a valid address
					{
						newReg.index = (parseInt(reg.address) - newGroup.params.address) * 2
					}
					else // we have no valid register or address
					{
						console.log('IoRegistersInit Error: group', iRegGroup, 'device', device, 'register', iReg, newReg.name, 'has no valid `index`, `register`, or `address` property')
						return
					}
				}
				newReg.raw = null
				newReg.address = newGroup.params.address + parseInt(newReg.index / 2)
			}


			if(registersNew[device][reg.name] !== undefined)
			{
				console.log('Register already exists:', device + '.' + reg.name)
				return
			}
			registersNew[device][reg.name] = newReg
			newGroup.registers.push(newReg)
			n++
		})
		registerListClean.push(newGroup)
	})
	// const devicesList = Object.keys(registersNew)
	// console.log('Register list n:', n, registersNew)

	// Do a second pass to resolve inputDevice.inputRegister for writable registers
	Object.keys(registersNew).forEach(deviceKey => {
		const device = registersNew[deviceKey]
		Object.keys(device).forEach(regName => {
			const reg = device[regName]
			if(reg.write && reg.inputRegister)
			{
				try {
					const iDev = registersNew[reg.inputDevice]
					if(!iDev) throw new Error('device "'+reg.inputDevice+'" not found')
					const iReg = iDev[reg.inputRegister]
					if(!iReg) throw new Error('register "'+reg.inputRegister+'" not found')
					reg._inputRegister = iReg
// console.log('IoRegistersInit write reg', reg)
// process.exit(0)
				} catch(err) {
					console.error('IoRegistersInit Error: write register['+deviceKey+']['+regName+'] unable to find register['+reg.inputDevice+']['+reg.inputRegister+']', err)
					// process.exit(1)
				}
			}
		})
	})

	return { registersObject: registersNew, registersArray: registerListClean }
}

module.exports = {
	init,
}

