//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// CurvesInit.js
//
// Load the curves JSON file and initialize in Global
//
//=============================================================================================
console.log('========== CurvesInit.js ===========')
const fs = require('fs')
const { curves }      = require('./Global.js')
const { clearObject } = require('./Util.js')
// const { CurvesFile }  = require('./Project.js')
const { ProjectDefinitions } = require('./Project.js')
const CurvesFile = ProjectDefinitions.controlCurvesFile

module.exports = function()
{
	// Try to load the file
	try {
		fileStr = fs.readFileSync(CurvesFile, 'utf8')
		const curvesArray = JSON.parse(fileStr)
		clearObject(curves)

		curvesArray.forEach(curve => {
			curves[curve.name] = curve.data
		})
		// console.log('CurvesInit loaded:', CurvesFile, curves)
	} catch (err) {
		console.error('Error loading ' + CurvesFile, err)
	}
	return curves
}
