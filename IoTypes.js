//*********************************************************************************************
//* COPYRIGHT © 2025-, Michael <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of Michael <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
// IoTypes.js
//
// Defines standard Modbus IO types and how to process them
//******************************************************************************************

const allIoTypes = require('./ModbusIoTypes.js') // All we have so far is Modbus IO

/**
 * Modifies a destination register object (`regDst`) by applying an IoType defined in a source register object (`regSrc`).
 *
 * The function looks up the IoType by name from the `allIoTypes` collection and assigns the appropriate `parse` method
 * to `regDst`. If the IoType is an object and has an `init` function, it is also invoked.
 *
 * @param {Object} regSrc - The source register object containing the IoType identifier (as a string) under the `type` property.
 * @param {Object} regDst - The destination register object to be modified by the IoType.
 * @param {Object} [options] - Optional configuration object passed to the IoType's `init` method, if applicable.
 *
 * @returns {false|string} - Returns `false` if the IoType was successfully applied, or an error message string if an error occurred.
 */
function applyIoType(regSrc, regDst, options)
{
	const type = regSrc.type
	if(typeof type !== 'string')
	{
		const errMsg = 'IoType must be a string, ' + typeof type + ' given'
		console.log('applyIoType error: ', errMsg)
		return errMsg
	}
	else if(type in allIoTypes)
	{
		const myIoType = allIoTypes[type]
		if(typeof myIoType === 'object')
		{
			if(typeof myIoType.init === 'function')
			{
				myIoType.init(regSrc, regDst, options)
			}
			regDst.parse = myIoType.parse
		}
		else if(typeof myIoType === 'function')
		{
			regDst.parse = myIoType
		}
		else
		{
			const errMsg = 'Error in types definitions for: "' + type + '"'
			console.log('applyIoType error: ', errMsg)
			return errMsg
		}
		return false
	}

	const errMsg = 'Unknown Io Type: ' + type
	console.log('applyIoType error: ', errMsg)
	return errMsg
}

module.exports = {
	allIoTypes,
	applyIoType,
}
