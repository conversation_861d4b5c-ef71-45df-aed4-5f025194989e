//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// ModbusOperations.js
//
// Low level Modbus communication functions
//
//=============================================================================================
// Adapted from: https://chatgpt.com/c/67ec52d7-0e8c-800b-b993-fe2667c341f4
//
// MA modifications for Promise based functions and proper timeout, socket closure, etc.
console.log('========== ModbusOperations.js =====')

const net = require('net')
// const { req } = require('pino-std-serializers')

/**
 * Class representing a Modbus TCP client.
 */
class ModbusClient {
	/**
	 * Create a ModbusClient instance.
	 * @param {string} ip - The IP address of the Modbus server.
	 * @param {number} [port=502] - The Modbus TCP port.
	 * @param {number} [timeout=5000] - Timeout in milliseconds.
	 */
	constructor(ip, port = 502, timeout = 5000) {
		this.ip = ip
		this.port = port
		this.timeout = timeout
		// this.connected = false
		// this.connecting = false
		this.sockets = 0
		this.transactionId = 1 // Initialize transaction ID
		this.lastRequestTransactionId = null
		this.dataCallback = null
		this.closeCallback = null
		// this.timeoutCallback = null
		// this.errorCallback = null
		this.errorValue = null
		this.receiveBuffer = Buffer.alloc(0)
		this.client = null
	}

	/**
	 * Connect to the Modbus server.
	 * @returns {Promise<void>} Resolves when connected, rejects on error.
	 */
	connect() {
		// if (this.connected || this.connecting) {
		// 	return
		// }
		// this.connecting = true
		// this.client = new net.Socket()
		// this.client.setNoDelay(true)
		// this.client.setTimeout(this.timeout)
//
		return new Promise((resolve, reject) => {
			// Create the client if it doesn't exist
			if(this.client)
			{
				resolve(true)
				return
			}

			let myReject  = reject

			this.errorValue = null

			this.client = new net.Socket()
			this.sockets++
			// console.log('New Modbus Socket:', this.ip + ':' + this.port)

			// Set up the event listeners and connect

			this.client.on('connect', () => {
				console.log('--> Modbus connect:', this.ip + ':' + this.port)
				myReject = null
				resolve(false)
			})

			this.client.on('data', (data) => { this.dataCallback?.(data) })

			this.client.on('error', err => {
				this.errorValue = err
				this.client.destroy()
			})

			this.client.on('timeout', () => {
				this.errorValue = new Error('Modbus timeout: ' + this.ip + ':' + this.port)
				this.client.destroy()
			})

			this.client.on('close', () => {
				this.client.removeAllListeners()
				this.client = null
				console.log('<-- Modbus close event: ' + this.ip + ':' + this.port)
				if(myReject)
				{
					myReject(this.errorValue || new Error('Modbus closed: ' + this.ip + ':' + this.port))
					myReject = null
				}
				else if(this.closeCallback)
				{
					this.closeCallback(this.errorValue)
				}
				else if(this.errorValue)
				{
					// throw this.errorValue
					console.error('Modbus close: ' + this.errorValue)
				}
			})

			console.log('--> Connecting to Modbus TCP device: ' + this.ip + ':' + this.port)

			this.client.connect({
				host: this.ip,
				port: this.port,
				timeout: this.timeout
			})

		})
	}

	/**
	 * Build a Modbus TCP request.
	 * @param {number} unitId - Modbus Unit ID.
	 * @param {number} functionCode - Modbus function code.
	 * @param {number} startAddress - Start address of registers.
	 * @param {number} numRegisters - Number of registers to read.
	 * @returns {Buffer} The Modbus request buffer.
	 */
	buildModbusRequest(unitId, functionCode, startAddress, numRegisters) {
		const request = Buffer.alloc(12)
		request.writeUInt16BE(this.transactionId, 0) // Transaction ID
		request.writeUInt16BE(0x0000, 2) // Protocol ID (0 for Modbus TCP)
		request.writeUInt16BE(0x0006, 4) // Length (bytes after this field)
		request.writeUInt8(unitId, 6) // Unit ID
		request.writeUInt8(functionCode, 7) // Function Code
		request.writeUInt16BE(startAddress, 8) // Start Address
		request.writeUInt16BE(numRegisters, 10) // Number of Registers

		this.lastRequestTransactionId = this.transactionId
		this.transactionId = (this.transactionId + 1) & 0xffff // Increment and wrap at 65535
		return request
	}

	/**
	 * Read registers from the Modbus server.
	 * @param {number} unitId - Modbus Unit ID.
	 * @param {number} functionCode - Modbus function code.
	 * @param {number} startAddress - Start address of registers.
	 * @param {number} numRegisters - Number of registers to read.
	 * @returns {Promise<{header: object, data: Buffer}>} Resolves with header and data separately, rejects on error.
	 */
	readRegisters(unitId, functionCode, startAddress, numRegisters) {
		return this.connect().then(wasConnected => {
			return new Promise((resolve, reject) => {
				this.client.setTimeout(this.timeout)

				// console.log('readRegisters wasConnected', wasConnected)

				// Make sure the buffer is cleared before sending a new request
				this.receiveBuffer = Buffer.alloc(0)

				// Build the request
				const request = this.buildModbusRequest(unitId, functionCode, startAddress, numRegisters)

				this.closeCallback = (err) => {
					this.closeCallback = null
					this.dataCallback = null
					reject(err || new Error('Modbus readRegisters closed: ' + this.ip + ':' + this.port))
				}

				this.dataCallback = data => {

					// console.log('readRegisters onData', this.ip + ':' + this.port + ', n:', data.length )

					this.receiveBuffer = Buffer.concat([this.receiveBuffer, data])

					if (this.receiveBuffer.length >= 9) {
						const length = this.receiveBuffer.readUInt16BE(4)
						const totalLength = 6 + length // 6 byte header + length

						if (this.receiveBuffer.length >= totalLength) {
							const transactionId = this.receiveBuffer.readUInt16BE(0)
							if (transactionId !== this.lastRequestTransactionId) {
								this.client.destroy()
								this.dataCallback = null
								this.closeCallback = null
								this.client.setTimeout(0)
								reject(new Error('Mismatched transaction ID in response'))
								return
							}

							const response = this.receiveBuffer.slice(0, totalLength)
							this.receiveBuffer = this.receiveBuffer.slice(totalLength)

							const header = {
								transactionId: response.readUInt16BE(0),
								protocolId: response.readUInt16BE(2),
								length: response.readUInt16BE(4),
								unitId: response.readUInt8(6),
								functionCode: response.readUInt8(7),
								byteCount: response.readUInt8(8)
							}

							const responseData = response.slice(9)

							console.log(Date.now().toString().slice(-4), ' readRegisters:', this.ip + ':' + this.port + '| a:', startAddress, '| n:', responseData.length )

							this.client.setTimeout(0)
							this.dataCallback = null
							this.closeCallback = null
							resolve({ header, data: responseData })
						}
					}
				}

				this.client.write(request)

			})
		}).catch(error => {
			console.error('==> Error in readRegisters:', error)
			// reject(error)
		})
	}


	/**
	 * Write one or more Modbus registers using a Buffer.
	 * @param {number} unitId - Modbus Unit ID.
	 * @param {number} startAddress - Start address to write to.
	 * @param {Buffer} dataBuffer - Buffer containing data to write. Must be even-length (padded if necessary).
	 * @returns {Promise<void>} Resolves when the write is successful.
	 */
	writeRegisters(unitId, startAddress, dataBuffer) {
		return this.connect().then(wasConnected => {
			return new Promise((resolve, reject) => {

				this.client.setTimeout(this.timeout)

				if(dataBuffer.length & 1) // make sure the buffer is multiple of 16 bits
				{
					dataBuffer = Buffer.concat([dataBuffer, Buffer.alloc(1, 0)])
				}

				const byteCount     = dataBuffer.length
				const numRegisters  = byteCount >> 1
				const requestLength = 7 + byteCount
				const request = Buffer.alloc(13 + byteCount)

				request.writeUInt16BE(this.transactionId, 0)
				request.writeUInt16BE(0x0000, 2)
				request.writeUInt16BE(requestLength, 4)
				request.writeUInt8(unitId, 6)
				request.writeUInt8(0x10, 7) // Function code for Write Multiple Registers
				request.writeUInt16BE(startAddress, 8)
				request.writeUInt16BE(numRegisters, 10)
				request.writeUInt8(byteCount, 12)
				dataBuffer.copy(request, 13)

				this.lastRequestTransactionId = this.transactionId
				this.transactionId = (this.transactionId + 1) & 0xffff

				let responseBuffer = Buffer.alloc(0)

				this.closeCallback = (err) => {
					this.closeCallback = null
					this.dataCallback = null
					reject(err || new Error('Modbus writeRegisters closed: ' + this.ip + ':' + this.port))
				}

				this.dataCallback = data => {

					responseBuffer = Buffer.concat([responseBuffer, data])

					if(responseBuffer.length >= 12)
					{
						const transactionId = data.readUInt16BE(0)

						this.client.setTimeout(0)
						this.dataCallback = null
						this.closeCallback = null
						if (transactionId !== this.lastRequestTransactionId) {
							reject(new Error('Mismatched transaction ID in response, got:' + transactionId + ' != ' + this.lastRequestTransactionId))
						}
						else
						{
							resolve()
						}
					}
				}

				this.client.write(request)

			})
		}).catch(error => {
			console.error('==> Error in readRegisters:', error)
			// reject(error)
		})
	}




	/**
	 * Close the Modbus connection.
	 * @returns {Promise<void>} Resolves when the connection is closed.
	 */
	close() {
		return new Promise(resolve => {
			if(this.client)
			{
				if(this.client.destroyed)
				{
					this.client.removeAllListeners()
					this.client = null
					resolve()
				}
				else
				{
					this.closeCallback = (err) => {
						console.log('Modbus close()', this.ip + ':' + this.port, err)
						this.closeCallback = null
						resolve()
					}
					// This should fire the `close` event set up in the `connect` method
					this.client.destroy()
				}
			}
			else
			{
				resolve()
			}
		})
	}
}


/**
 * Convert a Modbus response buffer to an array of 16-bit unsigned integers.
 * @param {Buffer} buffer - The buffer containing Modbus response data.
 * @returns {number[]} An array of 16-bit unsigned integers.
 */
function parseModbusResponse(buffer) {
	const values = []
	for (let i = 0; i < buffer.length; i += 2) {
		values.push(buffer.readUInt16BE(i))
	}
	return values
}

// export {
// 	ModbusClient,
// 	parseModbusResponse
// }

module.exports = {
	ModbusClient,
	parseModbusResponse
}
