
//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// routes/data.js
//
//=============================================================================================
/**
 * @module routes/data
 */

const fs = require('fs')
const { Stream } = require('stream')
const path = require('path')
const { API_ROOT, mac } = require('../System.js')
const Project = require('../Project.js')
const {
	timestamp,
	registers,
	devicesList,
	projectSettings,
	datalogSettings,
	curves,
	trends,
	schedules,
	documents,
} = require('../Global.js')
const { DeviceRegisterStreamProcessor } = require('../DeviceRegisterStream.js')
const { DownloadDataStream } = require('../DownloadDataStream.js')
const { DatalogHandler } = require('../DatalogHandler.js')

//----------------------------------------------------------------------------------------------------
/**
 * Routes for the data page.
 * - Call using `fastify.register()`
 * @param {object} fastify - The Fastify instance
 * @param {object} options
 * @param {Function} done - The callback function to call when the plugin is registered
 */
//----------------------------------------------------------------------------------------------------
function dataRoutes(fastify, options, done) {

	// List available resources
	fastify.get(API_ROOT + '/info/datalog', (request, reply) => {
		return reply.send(datalogSettings)
	})

	fastify.get(API_ROOT + '/info', async (request, reply) => {
		const info = {
			timestamp: timestamp.s,
			mac: mac,
			devices: [...devicesList, 'schedules'],
			curves,
			trends,
			schedules,
			documents,
			projectSettings,
			datalogSettings,
		}
		return reply.send(info)
	})

	fastify.get(API_ROOT + '/index', async (request, reply) => {
		return reply.send(Project)
	})


	// Register state route
	fastify.get(API_ROOT + '/data/registers/:device', async (request, reply) => {
		const device = request.params.device
		const myRegObj = devicesList.includes(device) ? registers[device] : {}
		const r = []
		Object.keys(myRegObj).forEach(rname => {
			const reg = myRegObj[rname]
			r.push(reg)
		})
		return reply.send(r)
	})


	// "name": "ActualSpeedRPM",
    // "device": "Pump1",
    // "updated": 1744844883006,
    // "_value": 890,
    // "value": 890,
    // "display": null,
    // "units": "RPM",
    // "comment": "The current speed of the vfd in rpm.",
    // "scale": 1,
    // "offset": 0,
    // "error": false,
    // "index": 0,
    // "raw": 890,
    // "address": 100



	// Register state route for the `Register Status` page
	fastify.get(API_ROOT + '/status/registers/:device', async (request, reply) => {
		// const r = {
		// 	value: registers.AnalogIn.TS01.value,
		// 	asText: registers.AnalogIn.TS01.asText(),
		// 	updated: registers.AnalogIn.TS01.updated,
		// }
		const device = request.params.device
		const r = []

		if(device === 'schedules')
		{
			Object.keys(schedules).forEach((name, i) => {
				const schedule = schedules[name]
				val = schedule.value ? 'true' : 'false'
				r.push({
					i,
					name,
					device: 'schedules',
					value: schedule.value,
					asText: val,
					units: null,
					raw: val,
					display: null,
					updated: null,
					error: false,
					address: null,
					comment: schedule.description,
				})
			})
		}
		else
		{
			const myRegObj = devicesList.includes(device) ? registers[device] : {}
			Object.keys(myRegObj).forEach((rname, i) => {
				const reg = myRegObj[rname]
				r.push({
					i,
					name: reg.name,
					device: reg.device,
					value: reg.value,
					asText: reg.asTextNative(true),
					units: reg.units,
					raw: reg.raw,
					display: reg.display,
					updated: reg.updated,
					error: reg.error,
					address: reg.address,
					comment: reg.comment,
				})
			})
		}
		return reply.send(r)
	})

	// Dashboard version of the data
	fastify.get(API_ROOT + '/dashboard/data', async (request, reply) => {
		const data = {}
		Object.keys(registers).forEach(device => {
			const myRegObj = registers[device]
			const devData = {}
			Object.keys(myRegObj).forEach(rname => {
				const reg = myRegObj[rname]
				devData[reg.name] = {
					// value: reg.value,
					text: [ reg.asText(), reg.asTextAlternate() ],
				}
				if (reg.error) {
					devData[reg.name].error = 1
				}
			})
			data[device] = devData
		})
		return reply.send(data)
	})

	fastify.get(API_ROOT + '/dashboard/bg.svg', async (request, reply) => {
		const bg = fs.readFileSync(Project.Dashboards[0].graphics, 'utf8')
		return reply.type('image/svg+xml').send(bg)
	})

	//----------------------------------------------------------------------------------------------------
	// Main trend route
	//----------------------------------------------------------------------------------------------------
	const METADATA = JSON.parse(fs.readFileSync(datalogSettings.paths.metadata))

	fastify.get(API_ROOT + '/trends', async (request, reply) => {
		// Create a buffer to hold the response chunks
		const buffer = new Stream.Readable();
		buffer._read = () => { };
		reply.type('text/html; charset=UTF-8').send(buffer)

		/** t0 (start time), t1 (end time), type (trendType), resolution */
		const query = { ...request.query }
		console.log(query)

		const streamProcessor = new DeviceRegisterStreamProcessor(METADATA)
		streamProcessor.streamChainFiles(datalogSettings.paths.fine, query, buffer)

		// Handle the events of the request
		request.raw.on("close", () => {
			buffer.destroy();
		})
	})

	//----------------------------------------------------------------------------------------------------
	// Download Data route
	//----------------------------------------------------------------------------------------------------

	const datalogHandler = new DatalogHandler(datalogSettings.path, {
		csv: datalogSettings.paths.metacsv,
		json: datalogSettings.paths.metadata,
	})
	fastify.get('/api/download/available', async (_, reply) => {
		const availableRange = datalogHandler.getAvailableDataRange("fine")
		if (!availableRange) return reply.code(404).send({ error: 'No data available' });
		reply.send(availableRange);
	})

	fastify.get('/api/download', async (request, reply) => {
		const downloadDataStream = new DownloadDataStream(request.query, datalogHandler)

		const buffer = new Stream.Readable();
		buffer._read = () => { };
		reply.type('text/csv').header('Content-Disposition', `attachment; filename="${downloadDataStream.fileName}"`).send(buffer)

		downloadDataStream.startStream(buffer);

		// Handle the events of the request
		request.raw.on("close", () => {
			buffer.destroy();
		})
	});

	done()
}


module.exports = dataRoutes
