//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// Util.js
//
//=============================================================================================
console.log('========== Util.js =================')

// const { execSync: childProcessExec } = require("child_process")

const os = require('os')

/**
 * Convert two x/y points to a scale and offset value.
 *
 * Used for 4-20mA inputs.
 *
 * @example
 * /// Temperature sensor with the following specs:
 * /// -10°C at  4mA
 * /// +40°C at 20mA
 * pointsToScale(4, -10, 20, 40) returns {scale: 3.125, offset: -12.5}
 *
 * @example
 * /// For a 4-20mA input from a Brainchild device:
 * /// Digital value is 4095 at 20mA
 * /// Digital value is (4/20)*4095 at 4mA = 819
 * /// For the above sensor:
 * pointsToScale((4/20)*4095, -10, 4095, 40) returns {scale: 0.015262515262515262, offset: -12.5}
 *
 * @param {number} x0 - The first x-value (e.g., 4mA or digital value at 4mA)
 * @param {number} y0 - The first y-value (e.g., temperature at x0)
 * @param {number} x1 - The second x-value (e.g., 20mA or digital value at 20mA)
 * @param {number} y1 - The second y-value (e.g., temperature at x1)
 * @returns {{scale: number, offset: number}} The calculated scale and offset values
 */
function pointsToScale(x0, y0, x1, y1) {
	const dy = y1 - y0
	const dx = x1 - x0
	const scale = dy / dx
	// const offset = -x0 * scale
	const offset = (x1 * y0 - x0 * y1) / dx
	return { scale, offset }
}

/**
 * Convert a Modbus register number (offset 1) to an address.
 *
 * This function makes it clearer that a Modbus register is being referenced.
 *
 * @example
 * /// Convert Modbus register 30001 to an address
 * ModbusRegisterToAddress(30001); // returns 0
 *
 * @param {number} reg - The Modbus register number (offset 1)
 * @returns {number} The corresponding address
 */
function ModbusRegisterToAddress(reg) {
	return (reg % 10000) - 1
}

/**
 * Read a digital input from a buffer.
 *
 * This function is required because there is no built-in binary buffer parser.
 *
 * @param {Buffer} buffer - The buffer containing digital input data
 * @param {number} index - The index of the digital input to read
 * @returns {boolean} `true` if the bit at the given index is set, otherwise `false`
 */
function readDigital(buffer, index) {
	const mask = 1 << (index & 7)
	const i = index >> 3
	return (buffer.readUint8(i) & mask) !== 0
	// return 1
}

/**
 * Removes all properties from the given object.
 *
 * @param {Object} obj - The object to be cleared.
 */
function clearObject(obj) {
	Object.keys(obj).forEach(key => delete obj[key]);
}
// Clone an object
function cloneObject(obj) {
	return JSON.parse(JSON.stringify(obj))
}

/**
 * Starts an interval that aligns execution as close as possible to the start of each specified interval.
 *
 * @param {Function} callback - The function to be executed at each interval.
 * @param {number} interval - The interval time in milliseconds (default: 1000ms).
 * @returns {Function} A function to cancel the interval.
 */
function startAlignedInterval(callback, interval = 1000) {
	let timeoutId;

	/**
	 * Schedules the next execution to align with the next interval boundary.
	 */
	function scheduleNextTick() {
		const now = Date.now();
		const delay = interval - (now % interval); // Calculate delay using modulo

		timeoutId = setTimeout(() => {
			callback();
			scheduleNextTick();
		}, delay);
	}

	scheduleNextTick();

	return () => clearTimeout(timeoutId); // Return a function to cancel the interval
}

/**
 * Extracts the first MAC address found in a given text string.
 *
 * This function searches for a MAC address using a regular expression that
 * matches common MAC address formats, including:
 * - `XX:XX:XX:XX:XX:XX` (colon-separated)
 * - `XX-XX-XX-XX-XX-XX` (hyphen-separated)
 * - `XXXX.XXXX.XXXX` (dot-separated)
 *
 * @param {string} text - The input text to search for a MAC address.
 * @returns {string|null} The first MAC address found in uppercase format, or `null` if no match is found.
 */
function getFirstMacAddress(text) {
	// Regex to match common MAC address formats
	const macRegex = /\b([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})\b|\b([0-9A-Fa-f]{4}[.]){2}([0-9A-Fa-f]{4})\b/;

	// Find the first match in the text
	const match = text.match(macRegex);

	// Return the match if found, otherwise null
	return match ? match[0].toUpperCase() : null;
}

/**
 * Retrieves the MAC address of the system.
 *
 * @returns {string|null} The MAC address if found, otherwise null.
 */
function getSystemMacAddress() {
	const interfaces = os.networkInterfaces()
	for (const interfaceName in interfaces)
	{
		const interfaceInfo = interfaces[interfaceName]
		for (const info of interfaceInfo)
		{
			if (info.family === 'IPv4' && !info.internal)
			{
				return info.mac.toUpperCase();
			}
		}
	}
	return null;
}


//------------------------------------------------------------------------------------------
/**
 * Convert an array to an object referenced by one of the properties of the array objects
 * Note: this function quietly ignores errors, assumes the array is valid
 * @param {array} a - The array to convert
 * @param {string} prop  - The property of the array objects to use as the key
 * @returns {object} - The object created from the array
 */
//------------------------------------------------------------------------------------------
function arrayToObject(a, prop)
{
	const r = {}
	if(Array.isArray(a) && prop !== undefined && typeof prop === 'string')
	{
		a.forEach(e => {
			if(e[prop] !== undefined)
			{
				r[e[prop]] = e
			}
		})
	}
	return r
}

//------------------------------------------------------------------------------------------
/**
 * Clean an object so that keys in the exclude array are not present in the returned object
 * @param {object} o - the object to clean
 * @param {array||string} exclude - a key or array of keys (of type `string`) to exclude
 * @param {*} val - an optional value to overwrite the excluded keys with, instead of removing them
 * @returns {object} - the cleaned object
 */
//------------------------------------------------------------------------------------------
function objectExclude(o, exclude, val)
{
	// console.log('objectExclude CALLED', o, exclude)
	const r = {}
	let excl = exclude
	if(typeof exclude === 'string')
	{
		excl = [exclude]
	}

	if(typeof o === 'object' && Array.isArray(excl))
	{
		Object.keys(o).forEach(k => {
			if(!exclude.includes(k))
			{
				r[k] = o[k]
			}
			else if(val !== undefined)
			{
				r[k] = val
			}
		})
	}
	// console.log('objectExclude RETURN', r)
	return r
}

//------------------------------------------------------------------------------------------
/**
 * Clean an object array so that keys in the exclude array are not present in the returned object array
 * @param {array} a - the array of objects to clean
 * @param {array||string} exclude - a key or array of keys (of type `string`) to exclude
 * @param {*} val - an optional value to overwrite the excluded keys with, instead of removing them
 * @returns {object} - the cleaned object
 */
//------------------------------------------------------------------------------------------
function objectArrayExclude(a, exclude, val)
{
	const r = []
	if(Array.isArray(a))
	{
		a.forEach(e => {
			r.push(objectExclude(e, exclude, val))
		})
	}
	return r
}


/**
 * Creates a new object that includes only the specified keys from the original object.
 *
 * @param {Object} o - The original object from which to include properties.
 * @param {string|string[]} include - A key or an array of keys to include in the new object.
 * @returns {Object} A new object containing only the key-value pairs where the key is in the `include` list.
 */
function objectInclude(o, include)
{
	const r = {}
	const incl = typeof include === 'string' ? [include] : include
	if(typeof o === 'object')
	{
		Object.keys(o).forEach(k => {
			if(incl.includes(k))
			{
				r[k] = o[k]
			}
		})
	}
	return r
}

/**
 * Formats a number with comma separators for thousands and optional decimal places.
 *
 * This function formats a number with commas as thousand separators and provides
 * control over decimal places. A negative decimalPlaces value will trim trailing zeros.
 *
 * @param {number} num - The number to format
 * @param {number} [decimalPlaces] - Optional: Number of decimal places to display.
 *                                   If positive: shows exactly this many decimal places (pads with zeros if needed).
 *                                   If negative: shows up to this many decimal places (trims trailing zeros).
 *                                   If undefined/not provided: shows the number as-is without decimal formatting.
 * @returns {string} The formatted number string with commas and optional decimal places
 *
 * @example
 * /// Returns "1,234,567.8900"
 * numberWithCommas(1234567.89, 4);
 *
 * @example
 * /// Returns "1,234,568" (rounds up)
 * numberWithCommas(1234567.89, 0);
 *
 * @example
 * /// Returns "1,234,567.89" (trims trailing zeros)
 * numberWithCommas(1234567.89, -4);
 *
 * @example
 * /// Returns "1,234,567.89" (no decimal formatting)
 * numberWithCommas(1234567.89);
 *
 * @example
 * /// Returns "1,000,000"
 * numberWithCommas(1000000);
 */function numberWithCommas(num, decimalPlaces)
{
	let numberStr;

	if(typeof decimalPlaces === 'number')
	{
		if(decimalPlaces >= 0)
		{
			numberStr = num.toFixed(decimalPlaces);
		}
		else if(decimalPlaces < 0)
		{
			numberStr = num.toFixed(-decimalPlaces);
			// Remove trailing zeros and optional dot if nothing follows
			numberStr = numberStr.replace(/(\.\d*?[1-9])0+$/g, '$1').replace(/\.0+$/, '');
		}
		else
		{
			numberStr = num.toString();
		}
	}
	else
	{
		numberStr = num.toString();
	}

	const [integerPart, decimalPart] = numberStr.split('.');

	var formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

	return decimalPart ? formattedInteger + '.' + decimalPart : formattedInteger;
}

/**
 * Converts a string representation of a boolean or number to an actual boolean value.
 * - Parses the string (case-insensitive) and attempts to convert it to a number.
 * - Returns `true` for non-zero numbers, `false` for zero or NaN.
 * - Returns `false` if the parsing fails or if the input is not a valid string representation.
 *
 * @param {string} str - The string to convert to a boolean. The string is case-insensitive.
 * @returns {boolean} The boolean representation of the string. Returns `false` if the string cannot be parsed.
 *
 * @example
 * stringToBoolean("true");  // returns true
 * stringToBoolean("1");     // returns true
 * stringToBoolean("0");     // returns false
 * stringToBoolean("false"); // returns false
 * stringToBoolean("abc");   // returns false (invalid)
 */
function stringToBoolean(str)
{
	try {
		const r = Number(JSON.parse(str.toLowerCase()))
		return isNaN(r) ? false : !!r
	} catch(e) {
		return false
	}
}

function parseDuration(str)
{
	const n = parseInt(str)
	if(isNaN(n))
	{
		return typeof str === 'string' && str[0].toLowerCase() === 'e' ? 0 : null // An event has zero duration
	}
	if(typeof str === 'number')
	{
		return str
	}
	const modifier = str.slice(-1)
	if(!isNaN(modifier)) //  no modifier
	{
		return n
	}
	let r = null
	switch(modifier.toLowerCase())
	{
		case 'd':
			r = n * 24 * 60 * 60
			break
		case 'h':
			r = n * 60 * 60
			break
		case 'm':
			r = n * 60
			break
		case 's':
			r = n
			break
	}
	return r
}
function parseDurationMs(str)
{
	const r = parseDuration(str)
	return r === null ? null : r * 1000
}


module.exports = {
	pointsToScale,
	ModbusRegisterToAddress,
	readDigital,
	clearObject,
	cloneObject,
	startAlignedInterval,
	// execPromise,
	getFirstMacAddress,
	getSystemMacAddress,
	arrayToObject,
	objectExclude,
	objectArrayExclude,
	objectInclude,
	numberWithCommas,
	stringToBoolean,
	parseDuration,
	parseDurationMs,
}

